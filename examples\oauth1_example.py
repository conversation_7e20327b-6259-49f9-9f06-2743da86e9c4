#!/usr/bin/env python3
"""
OAuth 1.0a Example for Jira Server/Data Center

This example demonstrates how to use OAuth 1.0a authentication
with mcp-atlassian for Jira Server/Data Center instances.

Prerequisites:
1. Jira Server/Data Center with OAuth enabled
2. Application Link configured in Jira
3. RSA key pair generated and public key registered in Jira
4. OAuth consumer key from Application Links
5. Access token and secret obtained through OAuth flow

Environment Variables Required:
- JIRA_URL: Your Jira Server/Data Center URL
- JIRA_OAUTH1_CONSUMER_KEY: OAuth consumer key from Application Links
- JIRA_OAUTH1_PRIVATE_KEY_PATH: Path to your RSA private key file
- JIRA_OAUTH1_ACCESS_TOKEN: OAuth access token
- JIRA_OAUTH1_ACCESS_TOKEN_SECRET: OAuth access token secret
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path for local development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mcp_atlassian.jira.client import <PERSON>ra<PERSON><PERSON>
from mcp_atlassian.jira.config import <PERSON>raConfig


def check_oauth1_environment():
    """Check if all required OAuth1 environment variables are set."""
    required_vars = [
        "JIRA_URL",
        "JIRA_OAUTH1_CONSUMER_KEY", 
        "JIRA_OAUTH1_PRIVATE_KEY_PATH",
        "JIRA_OAUTH1_ACCESS_TOKEN",
        "JIRA_OAUTH1_ACCESS_TOKEN_SECRET"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 To set up OAuth1 authentication:")
        print("   1. Run the OAuth1 setup wizard:")
        print("      docker run --rm -it -p 8080:8080 ghcr.io/sooperset/mcp-atlassian:latest --oauth1-web-setup")
        print("   2. Or set the environment variables manually")
        return False
    
    return True


def test_oauth1_authentication():
    """Test OAuth1 authentication with Jira Server/Data Center."""
    print("🔐 Testing OAuth 1.0a Authentication")
    print("=" * 50)
    
    if not check_oauth1_environment():
        return False
    
    try:
        # Create configuration from environment
        print("📋 Loading OAuth1 configuration from environment...")
        config = JiraConfig.from_env()
        
        print(f"   ✅ Jira URL: {config.url}")
        print(f"   ✅ Consumer Key: {config.oauth1_consumer_key}")
        print(f"   ✅ Private Key Path: {config.oauth1_private_key_path}")
        print(f"   ✅ Access Token: {config.oauth1_access_token[:10]}...")
        print(f"   ✅ Auth Type: {config.auth_type}")
        
        # Verify configuration
        if not config.is_auth_configured():
            print("❌ OAuth1 configuration is incomplete")
            return False
        
        print("\n🔌 Initializing Jira client...")
        client = JiraClient(config=config)
        print("   ✅ Client initialized successfully")
        
        # Test authentication by getting current user info
        print("\n👤 Testing authentication - getting current user...")
        user_info = client.jira.myself()
        print(f"   ✅ Authenticated as: {user_info.get('displayName', 'Unknown')}")
        print(f"   📧 Email: {user_info.get('emailAddress', 'Unknown')}")
        print(f"   🆔 Username: {user_info.get('name', 'Unknown')}")
        
        # Test API access by getting projects
        print("\n📁 Testing API access - getting projects...")
        projects = client.jira.projects()
        print(f"   ✅ Retrieved {len(projects)} projects")
        
        if projects:
            print("   📋 Sample projects:")
            for project in projects[:3]:  # Show first 3 projects
                print(f"      - {project.get('key', 'N/A')}: {project.get('name', 'N/A')}")
        
        # Test search functionality
        print("\n🔍 Testing search functionality...")
        search_result = client.jira.search_issues("", maxResults=5)
        total_issues = search_result.get('total', 0)
        print(f"   ✅ Found {total_issues} total issues")
        
        if search_result.get('issues'):
            print("   📋 Sample issues:")
            for issue in search_result['issues'][:3]:  # Show first 3 issues
                key = issue.get('key', 'N/A')
                summary = issue.get('fields', {}).get('summary', 'N/A')
                print(f"      - {key}: {summary}")
        
        print("\n🎉 OAuth 1.0a authentication test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ OAuth1 authentication test failed: {e}")
        print("\n💡 Troubleshooting tips:")
        print("   - Verify your Jira URL is correct and accessible")
        print("   - Check that OAuth is enabled in Jira Application Links")
        print("   - Ensure your consumer key is registered correctly")
        print("   - Verify your private key matches the public key in Jira")
        print("   - Check that your access tokens are valid and not expired")
        return False


def demonstrate_oauth1_config():
    """Demonstrate different ways to configure OAuth1 authentication."""
    print("\n📝 OAuth1 Configuration Examples")
    print("=" * 50)
    
    print("1. Environment Variables Configuration:")
    print("""
export JIRA_URL="https://jira.your-company.com"
export JIRA_OAUTH1_CONSUMER_KEY="your-oauth-consumer-key"
export JIRA_OAUTH1_PRIVATE_KEY_PATH="/path/to/your/private_key.pem"
export JIRA_OAUTH1_ACCESS_TOKEN="your-oauth-access-token"
export JIRA_OAUTH1_ACCESS_TOKEN_SECRET="your-oauth-access-token-secret"
""")
    
    print("2. Programmatic Configuration:")
    print("""
from mcp_atlassian.jira.config import JiraConfig

config = JiraConfig(
    url="https://jira.your-company.com",
    auth_type="oauth1",
    oauth1_consumer_key="your-oauth-consumer-key",
    oauth1_private_key_path="/path/to/your/private_key.pem",
    oauth1_access_token="your-oauth-access-token",
    oauth1_access_token_secret="your-oauth-access-token-secret"
)
""")
    
    print("3. Docker Configuration:")
    print("""
{
  "mcpServers": {
    "mcp-atlassian": {
      "command": "docker",
      "args": [
        "run", "-i",
        "-e", "JIRA_URL",
        "-e", "JIRA_OAUTH1_CONSUMER_KEY",
        "-e", "JIRA_OAUTH1_PRIVATE_KEY_PATH",
        "-e", "JIRA_OAUTH1_ACCESS_TOKEN",
        "-e", "JIRA_OAUTH1_ACCESS_TOKEN_SECRET",
        "-v", "/path/to/your/private_key.pem:/app/private_key.pem:ro",
        "ghcr.io/sooperset/mcp-atlassian:latest"
      ],
      "env": {
        "JIRA_URL": "https://jira.your-company.com",
        "JIRA_OAUTH1_CONSUMER_KEY": "your-oauth-consumer-key",
        "JIRA_OAUTH1_PRIVATE_KEY_PATH": "/app/private_key.pem",
        "JIRA_OAUTH1_ACCESS_TOKEN": "your-oauth-access-token",
        "JIRA_OAUTH1_ACCESS_TOKEN_SECRET": "your-oauth-access-token-secret"
      }
    }
  }
}
""")


def main():
    """Main function to run the OAuth1 example."""
    print("🚀 MCP Atlassian OAuth 1.0a Example")
    print("=" * 50)
    print("This example demonstrates OAuth 1.0a authentication with Jira Server/Data Center")
    print()
    
    # Test OAuth1 authentication
    success = test_oauth1_authentication()
    
    # Show configuration examples
    demonstrate_oauth1_config()
    
    if success:
        print("\n✅ OAuth1 example completed successfully!")
        print("You can now use OAuth1 authentication with mcp-atlassian")
    else:
        print("\n❌ OAuth1 example failed")
        print("Please check your configuration and try again")
        sys.exit(1)


if __name__ == "__main__":
    main()
