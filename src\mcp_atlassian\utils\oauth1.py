"""OAuth 1.0a utilities for Jira Server/Data Center authentication.

This module provides utilities for OAuth 1.0a authentication with Jira Server/Data Center.
It handles:
- OAuth1 session configuration
- Token acquisition and management
- Session configuration for API clients
"""

import logging
import os
from pathlib import Path
from typing import Optional

import requests
from requests_oauthlib import OAuth1Session

# Configure logging
logger = logging.getLogger("mcp-atlassian.oauth1")


class OAuth1Config:
    """OAuth 1.0a configuration for Jira Server/Data Center.
    
    This class manages the OAuth1 configuration and tokens. It handles:
    - Authentication configuration (consumer credentials)
    - Token acquisition workflow
    - Session configuration for API calls
    """
    
    def __init__(
        self,
        consumer_key: str,
        private_key_path: str,
        jira_base_url: str,
        access_token: Optional[str] = None,
        access_token_secret: Optional[str] = None,
    ):
        """Initialize OAuth1 configuration.
        
        Args:
            consumer_key: The OAuth1 consumer key registered in Jira
            private_key_path: Path to the RSA private key file
            jira_base_url: Base URL of the Jira instance
            access_token: Optional pre-existing access token
            access_token_secret: Optional pre-existing access token secret
        """
        self.consumer_key = consumer_key
        self.private_key_path = private_key_path
        self.jira_base_url = jira_base_url
        self.access_token = access_token
        self.access_token_secret = access_token_secret
        self._private_key: Optional[str] = None
    
    @property
    def private_key(self) -> str:
        """Get the private key content, loading it if necessary.
        
        Returns:
            The RSA private key content as a string
            
        Raises:
            FileNotFoundError: If the private key file doesn't exist
            ValueError: If the private key file cannot be read
        """
        if self._private_key is None:
            try:
                with open(self.private_key_path, 'r') as key_file:
                    self._private_key = key_file.read()
                logger.debug(f"Loaded private key from {self.private_key_path}")
            except FileNotFoundError:
                error_msg = f"Private key file not found: {self.private_key_path}"
                logger.error(error_msg)
                raise FileNotFoundError(error_msg)
            except Exception as e:
                error_msg = f"Failed to read private key file: {e}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        return self._private_key
    
    def get_request_token(self) -> tuple[str, str]:
        """Get a request token from Jira.
        
        Returns:
            Tuple of (request_token, request_token_secret)
            
        Raises:
            requests.RequestException: If the request fails
            ValueError: If the response is invalid
        """
        oauth = OAuth1Session(
            client_key=self.consumer_key,
            signature_method='RSA-SHA1',
            rsa_key=self.private_key,
            callback_uri='oob'  # out of band
        )
        
        request_token_url = f"{self.jira_base_url}/plugins/servlet/oauth/request-token"
        
        try:
            logger.info("Requesting OAuth1 request token...")
            response = oauth.fetch_request_token(request_token_url)
            
            request_token = response.get('oauth_token')
            request_token_secret = response.get('oauth_token_secret')
            
            if not request_token or not request_token_secret:
                raise ValueError("Invalid response: missing token or secret")
                
            logger.info("Successfully obtained request token")
            return request_token, request_token_secret
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get request token: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting request token: {e}")
            raise ValueError(f"Failed to get request token: {e}")
    
    def get_authorization_url(self, request_token: str) -> str:
        """Get the authorization URL for the user to visit.
        
        Args:
            request_token: The request token obtained from get_request_token()
            
        Returns:
            The authorization URL
        """
        return f"{self.jira_base_url}/plugins/servlet/oauth/authorize?oauth_token={request_token}"
    
    def get_access_token(
        self, 
        request_token: str, 
        request_token_secret: str, 
        verifier: str
    ) -> tuple[str, str]:
        """Exchange the request token and verifier for an access token.
        
        Args:
            request_token: The request token
            request_token_secret: The request token secret
            verifier: The verification code from user authorization
            
        Returns:
            Tuple of (access_token, access_token_secret)
            
        Raises:
            requests.RequestException: If the request fails
            ValueError: If the response is invalid
        """
        oauth = OAuth1Session(
            client_key=self.consumer_key,
            signature_method='RSA-SHA1',
            rsa_key=self.private_key,
            resource_owner_key=request_token,
            resource_owner_secret=request_token_secret,
            verifier=verifier
        )
        
        access_token_url = f"{self.jira_base_url}/plugins/servlet/oauth/access-token"
        
        try:
            logger.info("Requesting OAuth1 access token...")
            response = oauth.fetch_access_token(access_token_url)
            
            access_token = response.get('oauth_token')
            access_token_secret = response.get('oauth_token_secret')
            
            if not access_token or not access_token_secret:
                raise ValueError("Invalid response: missing access token or secret")
                
            logger.info("Successfully obtained access token")
            
            # Store the tokens
            self.access_token = access_token
            self.access_token_secret = access_token_secret
            
            return access_token, access_token_secret
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get access token: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting access token: {e}")
            raise ValueError(f"Failed to get access token: {e}")
    
    def create_oauth_session(self) -> OAuth1Session:
        """Create an OAuth1Session configured for API calls.
        
        Returns:
            Configured OAuth1Session instance
            
        Raises:
            ValueError: If access tokens are not available
        """
        if not self.access_token or not self.access_token_secret:
            raise ValueError("Access token and secret are required for API calls")
        
        return OAuth1Session(
            client_key=self.consumer_key,
            signature_method='RSA-SHA1',
            rsa_key=self.private_key,
            resource_owner_key=self.access_token,
            resource_owner_secret=self.access_token_secret
        )


def configure_oauth1_session(session: requests.Session, oauth1_config: OAuth1Config) -> bool:
    """Configure a requests session with OAuth 1.0a authentication.
    
    This function configures the session to use OAuth1 authentication for all requests.
    
    Args:
        session: The requests session to configure
        oauth1_config: The OAuth1 configuration to use
        
    Returns:
        True if the session was successfully configured, False otherwise
    """
    try:
        if not oauth1_config.access_token or not oauth1_config.access_token_secret:
            logger.error("OAuth1 access token and secret are required")
            return False
        
        # Create OAuth1 auth object
        oauth1_auth = OAuth1Session(
            client_key=oauth1_config.consumer_key,
            signature_method='RSA-SHA1',
            rsa_key=oauth1_config.private_key,
            resource_owner_key=oauth1_config.access_token,
            resource_owner_secret=oauth1_config.access_token_secret
        ).auth
        
        # Configure the session
        session.auth = oauth1_auth
        
        logger.info("Successfully configured OAuth1 session for Jira API")
        return True
        
    except Exception as e:
        logger.error(f"Failed to configure OAuth1 session: {e}")
        return False


def get_oauth1_config_from_env() -> Optional[OAuth1Config]:
    """Create OAuth1Config from environment variables.
    
    Reads the following environment variables:
    - JIRA_OAUTH1_CONSUMER_KEY: OAuth1 consumer key
    - JIRA_OAUTH1_PRIVATE_KEY_PATH: Path to RSA private key file
    - JIRA_URL: Jira base URL
    - JIRA_OAUTH1_ACCESS_TOKEN: Optional access token
    - JIRA_OAUTH1_ACCESS_TOKEN_SECRET: Optional access token secret
    
    Returns:
        OAuth1Config instance if required variables are set, None otherwise
    """
    consumer_key = os.getenv("JIRA_OAUTH1_CONSUMER_KEY")
    private_key_path = os.getenv("JIRA_OAUTH1_PRIVATE_KEY_PATH")
    jira_url = os.getenv("JIRA_URL")
    access_token = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN")
    access_token_secret = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN_SECRET")
    
    if not all([consumer_key, private_key_path, jira_url]):
        return None
    
    return OAuth1Config(
        consumer_key=consumer_key,
        private_key_path=private_key_path,
        jira_base_url=jira_url,
        access_token=access_token,
        access_token_secret=access_token_secret
    )
