"""OAuth 1.0a setup wizard for Jira Server/Data Center authentication.

This module provides an interactive setup wizard to help users configure
OAuth 1.0a authentication for Jira Server/Data Center instances.
"""

import logging
import os
import webbrowser
from pathlib import Path
from typing import Optional

import click

from .oauth1 import OAuth1Config

# Configure logging
logger = logging.getLogger("mcp-atlassian.oauth1-setup")


def validate_private_key_file(private_key_path: str) -> bool:
    """Validate that the private key file exists and is readable.
    
    Args:
        private_key_path: Path to the private key file
        
    Returns:
        True if the file is valid, False otherwise
    """
    try:
        path = Path(private_key_path)
        if not path.exists():
            click.echo(f"❌ Private key file not found: {private_key_path}")
            return False
        
        if not path.is_file():
            click.echo(f"❌ Path is not a file: {private_key_path}")
            return False
        
        # Try to read the file
        with open(path, 'r') as f:
            content = f.read().strip()
            if not content:
                click.echo(f"❌ Private key file is empty: {private_key_path}")
                return False
            
            # Basic validation - should contain PEM markers
            if "-----BEGIN" not in content or "-----END" not in content:
                click.echo(f"❌ Private key file doesn't appear to be in PEM format: {private_key_path}")
                return False
        
        click.echo(f"✅ Private key file validated: {private_key_path}")
        return True
        
    except Exception as e:
        click.echo(f"❌ Error validating private key file: {e}")
        return False


def save_oauth1_config(
    jira_url: str,
    consumer_key: str,
    private_key_path: str,
    access_token: str,
    access_token_secret: str
) -> None:
    """Save OAuth1 configuration to environment file.
    
    Args:
        jira_url: Jira base URL
        consumer_key: OAuth1 consumer key
        private_key_path: Path to private key file
        access_token: OAuth1 access token
        access_token_secret: OAuth1 access token secret
    """
    config_dir = Path.home() / ".mcp-atlassian"
    config_dir.mkdir(exist_ok=True)
    
    env_file = config_dir / "oauth1.env"
    
    config_content = f"""# OAuth 1.0a Configuration for Jira Server/Data Center
# Generated by mcp-atlassian OAuth1 setup wizard

JIRA_URL={jira_url}
JIRA_OAUTH1_CONSUMER_KEY={consumer_key}
JIRA_OAUTH1_PRIVATE_KEY_PATH={private_key_path}
JIRA_OAUTH1_ACCESS_TOKEN={access_token}
JIRA_OAUTH1_ACCESS_TOKEN_SECRET={access_token_secret}

# To use this configuration, source this file or copy these variables
# to your environment or .env file:
# source ~/.mcp-atlassian/oauth1.env
"""
    
    with open(env_file, 'w') as f:
        f.write(config_content)
    
    click.echo(f"✅ OAuth1 configuration saved to: {env_file}")
    click.echo("\n📋 To use this configuration:")
    click.echo(f"   source {env_file}")
    click.echo("   OR copy the environment variables to your .env file")


@click.command()
@click.option(
    "--jira-url",
    prompt="Jira Server/Data Center URL",
    help="Base URL of your Jira Server/Data Center instance"
)
@click.option(
    "--consumer-key",
    prompt="OAuth1 Consumer Key",
    help="Consumer key registered in Jira Application Links"
)
@click.option(
    "--private-key-path",
    prompt="Private Key File Path",
    help="Path to the RSA private key file (.pem)"
)
@click.option(
    "--verbose", "-v",
    is_flag=True,
    help="Enable verbose logging"
)
def oauth1_setup(
    jira_url: str,
    consumer_key: str,
    private_key_path: str,
    verbose: bool
) -> None:
    """Interactive OAuth 1.0a setup wizard for Jira Server/Data Center.
    
    This wizard will guide you through the OAuth 1.0a authentication flow:
    1. Validate your configuration
    2. Get a request token
    3. Open the authorization URL in your browser
    4. Exchange the verifier for an access token
    5. Save the configuration
    """
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    click.echo("🔐 OAuth 1.0a Setup Wizard for Jira Server/Data Center")
    click.echo("=" * 60)
    
    # Validate inputs
    if not jira_url.startswith(('http://', 'https://')):
        click.echo("❌ Jira URL must start with http:// or https://")
        return
    
    if not validate_private_key_file(private_key_path):
        return
    
    # Create OAuth1 config
    try:
        oauth1_config = OAuth1Config(
            consumer_key=consumer_key,
            private_key_path=private_key_path,
            jira_base_url=jira_url.rstrip('/')
        )
    except Exception as e:
        click.echo(f"❌ Failed to create OAuth1 configuration: {e}")
        return
    
    click.echo("\n🔄 Step 1: Getting request token...")
    
    # Get request token
    try:
        request_token, request_token_secret = oauth1_config.get_request_token()
        click.echo(f"✅ Request token obtained: {request_token[:10]}...")
    except Exception as e:
        click.echo(f"❌ Failed to get request token: {e}")
        click.echo("\n💡 Troubleshooting tips:")
        click.echo("   - Check that your Jira URL is correct and accessible")
        click.echo("   - Verify that OAuth is enabled in Jira Application Links")
        click.echo("   - Ensure your consumer key is registered correctly")
        click.echo("   - Check that your private key matches the public key in Jira")
        return
    
    # Get authorization URL
    auth_url = oauth1_config.get_authorization_url(request_token)
    
    click.echo("\n🌐 Step 2: User authorization required")
    click.echo("Please authorize the application in your browser:")
    click.echo(f"   {auth_url}")
    
    # Try to open browser automatically
    try:
        webbrowser.open(auth_url)
        click.echo("✅ Browser opened automatically")
    except Exception:
        click.echo("⚠️  Could not open browser automatically")
        click.echo("Please copy and paste the URL above into your browser")
    
    # Get verifier from user
    click.echo("\n🔑 Step 3: Enter verification code")
    verifier = click.prompt(
        "After authorizing, enter the verification code shown",
        type=str
    ).strip()
    
    if not verifier:
        click.echo("❌ Verification code cannot be empty")
        return
    
    click.echo("\n🔄 Step 4: Getting access token...")
    
    # Get access token
    try:
        access_token, access_token_secret = oauth1_config.get_access_token(
            request_token, request_token_secret, verifier
        )
        click.echo(f"✅ Access token obtained: {access_token[:10]}...")
    except Exception as e:
        click.echo(f"❌ Failed to get access token: {e}")
        click.echo("\n💡 Troubleshooting tips:")
        click.echo("   - Check that you entered the correct verification code")
        click.echo("   - Ensure the verification code hasn't expired")
        click.echo("   - Try the authorization process again")
        return
    
    click.echo("\n💾 Step 5: Saving configuration...")
    
    # Save configuration
    try:
        save_oauth1_config(
            jira_url=jira_url,
            consumer_key=consumer_key,
            private_key_path=private_key_path,
            access_token=access_token,
            access_token_secret=access_token_secret
        )
    except Exception as e:
        click.echo(f"❌ Failed to save configuration: {e}")
        return
    
    click.echo("\n🎉 OAuth 1.0a setup completed successfully!")
    click.echo("\n📝 Next steps:")
    click.echo("   1. Source the configuration file or copy the variables to your .env")
    click.echo("   2. Test the connection with your Jira instance")
    click.echo("   3. Start using mcp-atlassian with OAuth1 authentication")


if __name__ == "__main__":
    oauth1_setup()
