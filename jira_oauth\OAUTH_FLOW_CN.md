# JIRA OAuth 流程详解

本文档详细说明了如何使用 OAuth1.0a 与 JIRA 进行认证的完整流程。

## OAuth1.0a 流程概述

OAuth1.0a 是一个三步认证流程：

1. **获取请求令牌** (Request Token)
2. **用户授权** (User Authorization)
3. **获取访问令牌** (Access Token)

## 详细步骤说明

### 第一步: 获取请求令牌

客户端向 JIRA 发送请求以获取临时的请求令牌。

**请求 URL**: 
```
{JIRA_BASE_URL}/plugins/servlet/oauth/request-token
```

**必需参数**:
- `oauth_consumer_key`: 在 JIRA 中注册的消费者密钥
- `oauth_signature_method`: 签名方法 (RSA-SHA1)
- `oauth_timestamp`: 时间戳
- `oauth_nonce`: 随机字符串
- `oauth_version`: OAuth 版本 (1.0)
- `oauth_signature`: RSA 签名
- `oauth_callback`: 回调 URL (使用 "oob" 表示手动输入验证码)

**响应**:
```
oauth_token={request_token}&oauth_token_secret={request_token_secret}
```

### 第二步: 用户授权

用户需要在浏览器中授权应用程序访问其 JIRA 账户。

**授权 URL**:
```
{JIRA_BASE_URL}/plugins/servlet/oauth/authorize?oauth_token={request_token}
```

用户访问此 URL 后，JIRA 会显示授权页面。用户确认授权后，会获得一个验证码 (verification code)。

### 第三步: 获取访问令牌

使用请求令牌和验证码获取永久的访问令牌。

**请求 URL**:
```
{JIRA_BASE_URL}/plugins/servlet/oauth/access-token
```

**必需参数**:
- 所有 OAuth 参数（与第一步相同）
- `oauth_token`: 第一步获得的请求令牌
- `oauth_verifier`: 用户授权后获得的验证码

**响应**:
```
oauth_token={access_token}&oauth_token_secret={access_token_secret}
```

## 代码示例

### 获取请求令牌
```python
from requests_oauthlib import OAuth1Session

# 创建 OAuth 会话
oauth = OAuth1Session(
    client_key=consumer_key,
    signature_method='RSA-SHA1',
    rsa_key=private_key,
    callback_uri='oob'
)

# 获取请求令牌
request_token_url = f"{jira_url}/plugins/servlet/oauth/request-token"
fetch_response = oauth.fetch_request_token(request_token_url)
```

### 用户授权
```python
# 构建授权 URL
authorization_url = f"{jira_url}/plugins/servlet/oauth/authorize?oauth_token={request_token}"
# 用户在浏览器中访问此 URL 并获取验证码
verifier = input("请输入验证码: ")
```

### 获取访问令牌
```python
# 创建新的 OAuth 会话（包含验证码）
oauth = OAuth1Session(
    client_key=consumer_key,
    signature_method='RSA-SHA1',
    rsa_key=private_key,
    resource_owner_key=request_token,
    resource_owner_secret=request_token_secret,
    verifier=verifier
)

# 获取访问令牌
access_token_url = f"{jira_url}/plugins/servlet/oauth/access-token"
oauth_tokens = oauth.fetch_access_token(access_token_url)
```

### 使用访问令牌进行 API 调用
```python
# 创建最终的 OAuth 会话
oauth = OAuth1Session(
    client_key=consumer_key,
    signature_method='RSA-SHA1',
    rsa_key=private_key,
    resource_owner_key=access_token,
    resource_owner_secret=access_token_secret
)

# 调用 JIRA API
response = oauth.get(f"{jira_url}/rest/api/2/myself")
```

## 常见问题

### 1. "Consumer key does not exist"
确保在 JIRA 中正确配置了消费者密钥和公钥。

### 2. "Invalid signature"
检查以下内容：
- 私钥是否正确
- 时间戳是否同步
- 签名方法是否为 RSA-SHA1

### 3. "Invalid OAuth verifier"
确保使用的是正确的验证码，且未过期。

## 安全注意事项

1. **保护私钥**：私钥绝不能泄露
2. **HTTPS 传输**：所有通信应使用 HTTPS
3. **令牌存储**：安全存储访问令牌
4. **定期刷新**：定期更新访问令牌

## 参考资料

- [JIRA REST API 文档](https://developer.atlassian.com/cloud/jira/platform/rest/)
- [OAuth1.0a 规范](https://tools.ietf.org/html/rfc5849)