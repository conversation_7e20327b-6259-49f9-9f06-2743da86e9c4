#!/usr/bin/env python3
"""
使用验证码获取访问令牌的脚本
在获取请求令牌并授权后，使用验证码获取访问令牌
"""

import requests
from requests_oauthlib import OAuth1Session

def get_jira_access_token(jira_base_url, consumer_key, private_key_path, 
                         request_token, request_token_secret, verifier):
    """
    使用验证码获取访问令牌
    
    参数:
        jira_base_url (str): JIRA 实例的基础 URL
        consumer_key (str): 消费者密钥
        private_key_path (str): 私钥文件路径
        request_token (str): 之前获取的请求令牌
        request_token_secret (str): 请求令牌密钥
        verifier (str): 授权后获得的验证码
    
    返回:
        tuple: (access_token, access_token_secret) 或 (None, None) 如果失败
    """
    
    # 1. 读取私钥文件
    try:
        with open(private_key_path, 'r') as key_file:
            private_key = key_file.read()
    except FileNotFoundError:
        print(f"错误: 找不到私钥文件 {private_key_path}")
        return None, None
    except Exception as e:
        print(f"错误: 读取私钥文件失败: {e}")
        return None, None
    
    # 2. 创建 OAuth1Session（包含请求令牌和验证码）
    oauth = OAuth1Session(
        client_key=consumer_key,
        signature_method='RSA-SHA1',
        rsa_key=private_key,
        resource_owner_key=request_token,
        resource_owner_secret=request_token_secret,
        verifier=verifier
    )
    
    # 3. 构建访问令牌 URL
    access_token_url = f"{jira_base_url}/plugins/servlet/oauth/access-token"
    
    # 4. 发送请求获取访问令牌
    try:
        print("正在获取访问令牌...")
        response = oauth.fetch_access_token(access_token_url)
        
        # 5. 提取访问令牌和密钥
        access_token = response.get('oauth_token')
        access_token_secret = response.get('oauth_token_secret')
        
        if access_token and access_token_secret:
            print("✓ 成功获取访问令牌!")
            print(f"  访问令牌: {access_token}")
            print(f"  令牌密钥: {access_token_secret}")
            return access_token, access_token_secret
        else:
            print("✗ 响应中缺少令牌信息")
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求失败: {e}")
        return None, None
    except Exception as e:
        print(f"✗ 获取访问令牌失败: {e}")
        return None, None

# 使用示例
if __name__ == "__main__":
    # 配置信息 - 请根据实际情况修改
    JIRA_URL = "https://jira.qianxin-inc.cn"  # Replace with your JIRA URL
    CONSUMER_KEY = "mcp-atlassian-oauth-client"  # Replace with your consumer key
    PRIVATE_KEY_FILE = "../jira_privatekey.pem"  # Path to your private key file
    
    # 从 get_request_token_simple.py 获取的请求令牌信息
    REQUEST_TOKEN = "51NPp9hYKuYmHUdE293ZhnKlxx78O4PW"  # 替换为您获取到的请求令牌
    REQUEST_TOKEN_SECRET = "LGixld70OGHUwesZt8SlWi7Slz2bHi3J"  # 替换为您获取到的请求令牌密钥
    
    # 用户输入的验证码
    VERIFIER = input("请输入授权后获得的验证码: ").strip()
    
    if not VERIFIER:
        print("错误: 验证码不能为空")
        exit(1)
    
    print("\nJIRA 访问令牌获取工具")
    print("=" * 30)
    
    # 获取访问令牌
    access_token, access_token_secret = get_jira_access_token(
        JIRA_URL, CONSUMER_KEY, PRIVATE_KEY_FILE,
        REQUEST_TOKEN, REQUEST_TOKEN_SECRET, VERIFIER
    )
    
    if access_token and access_token_secret:
        print("\n访问令牌已成功获取，可用于调用 JIRA API")
    else:
        print("\n获取访问令牌失败")