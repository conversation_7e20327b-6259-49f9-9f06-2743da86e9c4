"""Tests for OAuth1 authentication utilities."""

import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
import requests
from requests_oauthlib import OAuth1Session

from mcp_atlassian.utils.oauth1 import (
    OAuth1Config,
    configure_oauth1_session,
    get_oauth1_config_from_env,
)


@pytest.fixture
def temp_private_key():
    """Create a temporary private key file for testing."""
    private_key_content = """-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA1234567890abcdef...
-----END RSA PRIVATE KEY-----"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as f:
        f.write(private_key_content)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    os.unlink(temp_path)


@pytest.fixture
def oauth1_config(temp_private_key):
    """Create an OAuth1Config instance for testing."""
    return OAuth1Config(
        consumer_key="test_consumer_key",
        private_key_path=temp_private_key,
        jira_base_url="https://jira.example.com",
        access_token="test_access_token",
        access_token_secret="test_access_token_secret"
    )


class TestOAuth1Config:
    """Test cases for OAuth1Config class."""
    
    def test_init(self, temp_private_key):
        """Test OAuth1Config initialization."""
        config = OAuth1Config(
            consumer_key="test_key",
            jira_base_url="https://jira.example.com",
            private_key_path=temp_private_key
        )

        assert config.consumer_key == "test_key"
        assert config.private_key_path == temp_private_key
        assert config.jira_base_url == "https://jira.example.com"
        assert config.access_token is None
        assert config.access_token_secret is None

    def test_init_with_private_key_content(self):
        """Test OAuth1Config initialization with private key content."""
        private_key_content = "-----BEGIN RSA PRIVATE KEY-----\ntest_key\n-----END RSA PRIVATE KEY-----"
        config = OAuth1Config(
            consumer_key="test_key",
            jira_base_url="https://jira.example.com",
            private_key_content=private_key_content
        )

        assert config.consumer_key == "test_key"
        assert config.private_key_path is None
        assert config.jira_base_url == "https://jira.example.com"
        assert config._private_key == private_key_content
        assert config.private_key == private_key_content

    def test_init_missing_private_key(self):
        """Test OAuth1Config initialization without private key."""
        with pytest.raises(ValueError, match="Either private_key_path or private_key_content must be provided"):
            OAuth1Config(
                consumer_key="test_key",
                jira_base_url="https://jira.example.com"
            )
    
    def test_private_key_property(self, oauth1_config):
        """Test private key loading."""
        private_key = oauth1_config.private_key
        assert "-----BEGIN RSA PRIVATE KEY-----" in private_key
        assert "-----END RSA PRIVATE KEY-----" in private_key
    
    def test_private_key_file_not_found(self):
        """Test error handling when private key file doesn't exist."""
        config = OAuth1Config(
            consumer_key="test_key",
            private_key_path="/nonexistent/path.pem",
            jira_base_url="https://jira.example.com"
        )
        
        with pytest.raises(FileNotFoundError, match="Private key file not found"):
            _ = config.private_key
    
    @patch('mcp_atlassian.utils.oauth1.OAuth1Session')
    def test_get_request_token_success(self, mock_oauth_session, oauth1_config):
        """Test successful request token retrieval."""
        # Mock OAuth1Session
        mock_session = Mock()
        mock_oauth_session.return_value = mock_session
        mock_session.fetch_request_token.return_value = {
            'oauth_token': 'test_request_token',
            'oauth_token_secret': 'test_request_secret'
        }
        
        token, secret = oauth1_config.get_request_token()
        
        assert token == 'test_request_token'
        assert secret == 'test_request_secret'
        
        # Verify OAuth1Session was configured correctly
        mock_oauth_session.assert_called_once_with(
            client_key='test_consumer_key',
            signature_method='RSA-SHA1',
            rsa_key=oauth1_config.private_key,
            callback_uri='oob'
        )
    
    @patch('mcp_atlassian.utils.oauth1.OAuth1Session')
    def test_get_request_token_failure(self, mock_oauth_session, oauth1_config):
        """Test request token retrieval failure."""
        mock_session = Mock()
        mock_oauth_session.return_value = mock_session
        mock_session.fetch_request_token.side_effect = requests.RequestException("Network error")
        
        with pytest.raises(requests.RequestException):
            oauth1_config.get_request_token()
    
    def test_get_authorization_url(self, oauth1_config):
        """Test authorization URL generation."""
        url = oauth1_config.get_authorization_url("test_token")
        expected = "https://jira.example.com/plugins/servlet/oauth/authorize?oauth_token=test_token"
        assert url == expected
    
    @patch('mcp_atlassian.utils.oauth1.OAuth1Session')
    def test_get_access_token_success(self, mock_oauth_session, oauth1_config):
        """Test successful access token retrieval."""
        mock_session = Mock()
        mock_oauth_session.return_value = mock_session
        mock_session.fetch_access_token.return_value = {
            'oauth_token': 'new_access_token',
            'oauth_token_secret': 'new_access_secret'
        }
        
        token, secret = oauth1_config.get_access_token(
            'request_token', 'request_secret', 'verifier'
        )
        
        assert token == 'new_access_token'
        assert secret == 'new_access_secret'
        assert oauth1_config.access_token == 'new_access_token'
        assert oauth1_config.access_token_secret == 'new_access_secret'
    
    def test_create_oauth_session_success(self, oauth1_config):
        """Test OAuth session creation with valid tokens."""
        with patch('mcp_atlassian.utils.oauth1.OAuth1Session') as mock_oauth_session:
            mock_session = Mock()
            mock_oauth_session.return_value = mock_session
            
            session = oauth1_config.create_oauth_session()
            
            assert session == mock_session
            mock_oauth_session.assert_called_once_with(
                client_key='test_consumer_key',
                signature_method='RSA-SHA1',
                rsa_key=oauth1_config.private_key,
                resource_owner_key='test_access_token',
                resource_owner_secret='test_access_token_secret'
            )
    
    def test_create_oauth_session_no_tokens(self, temp_private_key):
        """Test OAuth session creation without access tokens."""
        config = OAuth1Config(
            consumer_key="test_key",
            private_key_path=temp_private_key,
            jira_base_url="https://jira.example.com"
        )
        
        with pytest.raises(ValueError, match="Access token and secret are required"):
            config.create_oauth_session()


class TestConfigureOAuth1Session:
    """Test cases for configure_oauth1_session function."""
    
    @patch('mcp_atlassian.utils.oauth1.OAuth1Session')
    def test_configure_session_success(self, mock_oauth_session, oauth1_config):
        """Test successful session configuration."""
        mock_session_instance = Mock()
        mock_auth = Mock()
        mock_session_instance.auth = mock_auth
        mock_oauth_session.return_value = mock_session_instance
        
        session = Mock()
        result = configure_oauth1_session(session, oauth1_config)
        
        assert result is True
        assert session.auth == mock_auth
    
    def test_configure_session_no_tokens(self, temp_private_key):
        """Test session configuration without access tokens."""
        config = OAuth1Config(
            consumer_key="test_key",
            private_key_path=temp_private_key,
            jira_base_url="https://jira.example.com"
        )
        
        session = Mock()
        result = configure_oauth1_session(session, config)
        
        assert result is False


class TestGetOAuth1ConfigFromEnv:
    """Test cases for get_oauth1_config_from_env function."""
    
    def test_complete_config(self, temp_private_key):
        """Test loading complete OAuth1 config from environment."""
        with patch.dict(os.environ, {
            'JIRA_OAUTH1_CONSUMER_KEY': 'test_consumer',
            'JIRA_OAUTH1_PRIVATE_KEY_PATH': temp_private_key,
            'JIRA_URL': 'https://jira.example.com',
            'JIRA_OAUTH1_ACCESS_TOKEN': 'test_token',
            'JIRA_OAUTH1_ACCESS_TOKEN_SECRET': 'test_secret'
        }, clear=True):
            config = get_oauth1_config_from_env()
            
            assert config is not None
            assert config.consumer_key == 'test_consumer'
            assert config.private_key_path == temp_private_key
            assert config.jira_base_url == 'https://jira.example.com'
            assert config.access_token == 'test_token'
            assert config.access_token_secret == 'test_secret'
    
    def test_minimal_config(self, temp_private_key):
        """Test loading minimal OAuth1 config from environment."""
        with patch.dict(os.environ, {
            'JIRA_OAUTH1_CONSUMER_KEY': 'test_consumer',
            'JIRA_OAUTH1_PRIVATE_KEY_PATH': temp_private_key,
            'JIRA_URL': 'https://jira.example.com'
        }, clear=True):
            config = get_oauth1_config_from_env()
            
            assert config is not None
            assert config.consumer_key == 'test_consumer'
            assert config.private_key_path == temp_private_key
            assert config.jira_base_url == 'https://jira.example.com'
            assert config.access_token is None
            assert config.access_token_secret is None
    
    def test_missing_required_vars(self):
        """Test handling of missing required environment variables."""
        with patch.dict(os.environ, {}, clear=True):
            config = get_oauth1_config_from_env()
            assert config is None
        
        with patch.dict(os.environ, {
            'JIRA_OAUTH1_CONSUMER_KEY': 'test_consumer'
        }, clear=True):
            config = get_oauth1_config_from_env()
            assert config is None
