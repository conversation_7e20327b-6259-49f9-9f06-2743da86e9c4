#!/usr/bin/env python3
"""
简化版 JIRA 请求令牌获取脚本
专注于核心功能，便于理解和使用
"""

import requests
from requests_oauthlib import OAuth1Session

def get_jira_request_token(jira_base_url, consumer_key, private_key_path):
    """
    从 JIRA 获取请求令牌的简化函数
    
    参数:
        jira_base_url (str): JIRA 实例的基础 URL，例如 "https://your-domain.atlassian.net"
        consumer_key (str): 在 JIRA 中注册的消费者密钥
        private_key_path (str): 私钥文件路径
    
    返回:
        tuple: (request_token, request_token_secret) 或 (None, None) 如果失败
    """
    
    # 1. 读取私钥文件
    try:
        with open(private_key_path, 'r') as key_file:
            private_key = key_file.read()
    except FileNotFoundError:
        print(f"错误: 找不到私钥文件 {private_key_path}")
        return None, None
    except Exception as e:
        print(f"错误: 读取私钥文件失败: {e}")
        return None, None
    
    # 2. 创建 OAuth1Session
    oauth = OAuth1Session(
        client_key=consumer_key,
        signature_method='RSA-SHA1',
        rsa_key=private_key,
        callback_uri='oob'  # 使用 oob (out of band) 表示手动输入验证码
    )
    
    # 3. 构建请求令牌 URL
    request_token_url = f"{jira_base_url}/plugins/servlet/oauth/request-token"
    
    # 4. 发送请求获取令牌
    try:
        print("正在获取请求令牌...")
        response = oauth.fetch_request_token(request_token_url)
        
        # 5. 提取令牌和密钥
        request_token = response.get('oauth_token')
        request_token_secret = response.get('oauth_token_secret')
        
        if request_token and request_token_secret:
            print("✓ 成功获取请求令牌!")
            print(f"  令牌: {request_token}")
            print(f"  密钥: {request_token_secret}")
            return request_token, request_token_secret
        else:
            print("✗ 响应中缺少令牌信息")
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求失败: {e}")
        return None, None
    except Exception as e:
        print(f"✗ 获取请求令牌失败: {e}")
        return None, None

# 使用示例
if __name__ == "__main__":
    # 配置信息 - 请根据实际情况修改
    JIRA_URL = "https://jira.qianxin-inc.cn"  # Replace with your JIRA URL
    CONSUMER_KEY = "mcp-atlassian-oauth-client"  # Replace with your consumer key
    PRIVATE_KEY_FILE = "./jira_privatekey.pem"  # Path to your private key file
    
    print("JIRA 请求令牌获取工具")
    print("=" * 30)
    
    # 获取请求令牌
    token, secret = get_jira_request_token(JIRA_URL, CONSUMER_KEY, PRIVATE_KEY_FILE)
    
    if token and secret:
        # 显示下一步操作说明
        auth_url = f"{JIRA_URL}/plugins/servlet/oauth/authorize?oauth_token={token}"
        print("\n下一步:")
        print("1. 在浏览器中打开以下 URL 进行授权:")
        print(f"   {auth_url}")
        print("2. 授权后会获得一个验证码")
        print("3. 使用该验证码获取访问令牌")
    else:
        print("\n获取请求令牌失败，请检查配置信息")