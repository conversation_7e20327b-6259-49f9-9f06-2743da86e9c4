# .github/workflows/tests.yml

name: Run Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    name: Run pytest on Python ${{ matrix.python-version }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: "0.6.10"
        cache: true

    - name: Install dependencies
      run: uv sync --frozen --all-extras --dev

    - name: Run tests with pytest
      # Add -v for verbose output, helpful in CI
      # Add basic coverage reporting to terminal logs
      # Skip real API validation tests as they require credentials
      run: uv run pytest -v -k "not test_real_api_validation" --cov=src/mcp_atlassian --cov-report=term-missing
