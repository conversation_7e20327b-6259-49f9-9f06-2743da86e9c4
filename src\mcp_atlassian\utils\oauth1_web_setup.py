"""OAuth 1.0a Web Setup Wizard for Jira Server/Data Center authentication.

This module provides a web-based setup wizard to help users configure
OAuth 1.0a authentication for Jira Server/Data Center instances.
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Optional

import click
from fastapi import <PERSON><PERSON><PERSON>, Form, HTTPException, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

from .oauth1 import OAuth1Config

# Configure logging
logger = logging.getLogger("mcp-atlassian.oauth1-web-setup")

# Create FastAPI app
app = FastAPI(title="OAuth 1.0a Setup Wizard", description="Setup OAuth 1.0a for Jira Server/Data Center")

# Setup templates directory
templates_dir = Path(__file__).parent / "templates"
templates_dir.mkdir(exist_ok=True)
templates = Jinja2Templates(directory=str(templates_dir))

# Global state for the setup process
setup_state = {
    "oauth1_config": None,
    "request_token": None,
    "request_token_secret": None,
    "access_token": None,
    "access_token_secret": None,
    "error": None
}


@app.get("/", response_class=HTMLResponse)
async def setup_home(request: Request):
    """Home page of the OAuth1 setup wizard."""
    return templates.TemplateResponse("oauth1_setup.html", {
        "request": request,
        "step": "config",
        "error": setup_state.get("error")
    })


@app.post("/configure")
async def configure_oauth1(
    request: Request,
    jira_url: str = Form(...),
    consumer_key: str = Form(...),
    private_key: str = Form(...)
):
    """Configure OAuth1 settings and get request token."""
    try:
        # Clear any previous error
        setup_state["error"] = None
        
        # Validate inputs
        if not jira_url.startswith(('http://', 'https://')):
            raise HTTPException(status_code=400, detail="Jira URL must start with http:// or https://")
        
        if not consumer_key.strip():
            raise HTTPException(status_code=400, detail="Consumer key is required")
        
        if not private_key.strip():
            raise HTTPException(status_code=400, detail="Private key is required")
        
        # Validate private key format
        if "-----BEGIN" not in private_key or "-----END" not in private_key:
            raise HTTPException(status_code=400, detail="Private key must be in PEM format")
        
        # Create temporary file for private key
        with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as f:
            f.write(private_key)
            temp_key_path = f.name
        
        # Create OAuth1 config
        oauth1_config = OAuth1Config(
            consumer_key=consumer_key.strip(),
            private_key_path=temp_key_path,
            jira_base_url=jira_url.rstrip('/')
        )
        
        # Get request token
        request_token, request_token_secret = oauth1_config.get_request_token()
        
        # Store in global state
        setup_state.update({
            "oauth1_config": oauth1_config,
            "request_token": request_token,
            "request_token_secret": request_token_secret,
            "jira_url": jira_url.rstrip('/'),
            "consumer_key": consumer_key.strip(),
            "private_key_path": temp_key_path
        })
        
        # Get authorization URL
        auth_url = oauth1_config.get_authorization_url(request_token)
        
        return templates.TemplateResponse("oauth1_setup.html", {
            "request": request,
            "step": "authorize",
            "auth_url": auth_url,
            "request_token": request_token
        })
        
    except Exception as e:
        logger.error(f"Configuration failed: {e}")
        setup_state["error"] = str(e)
        return templates.TemplateResponse("oauth1_setup.html", {
            "request": request,
            "step": "config",
            "error": str(e)
        })


@app.post("/verify")
async def verify_and_get_access_token(
    request: Request,
    verifier: str = Form(...)
):
    """Verify the authorization code and get access token."""
    try:
        # Clear any previous error
        setup_state["error"] = None
        
        if not setup_state.get("oauth1_config") or not setup_state.get("request_token"):
            raise HTTPException(status_code=400, detail="Setup not properly initialized. Please start over.")
        
        if not verifier.strip():
            raise HTTPException(status_code=400, detail="Verification code is required")
        
        # Get access token
        oauth1_config = setup_state["oauth1_config"]
        access_token, access_token_secret = oauth1_config.get_access_token(
            setup_state["request_token"],
            setup_state["request_token_secret"],
            verifier.strip()
        )
        
        # Store tokens
        setup_state.update({
            "access_token": access_token,
            "access_token_secret": access_token_secret
        })
        
        # Generate configuration
        config_content = generate_config_content()
        
        return templates.TemplateResponse("oauth1_setup.html", {
            "request": request,
            "step": "complete",
            "config_content": config_content,
            "jira_url": setup_state["jira_url"],
            "consumer_key": setup_state["consumer_key"],
            "access_token": access_token,
            "access_token_secret": access_token_secret
        })
        
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        setup_state["error"] = str(e)
        return templates.TemplateResponse("oauth1_setup.html", {
            "request": request,
            "step": "authorize",
            "auth_url": setup_state["oauth1_config"].get_authorization_url(setup_state["request_token"]) if setup_state.get("oauth1_config") and setup_state.get("request_token") else "",
            "request_token": setup_state.get("request_token", ""),
            "error": str(e)
        })


@app.get("/restart")
async def restart_setup():
    """Restart the setup process."""
    # Clean up temporary files
    if setup_state.get("private_key_path") and os.path.exists(setup_state["private_key_path"]):
        try:
            os.unlink(setup_state["private_key_path"])
        except Exception:
            pass
    
    # Clear state
    setup_state.clear()
    
    return RedirectResponse(url="/", status_code=302)


def generate_config_content() -> str:
    """Generate configuration content for the user."""
    return f"""# OAuth 1.0a Configuration for Jira Server/Data Center
# Generated by mcp-atlassian OAuth1 Web Setup Wizard

JIRA_URL={setup_state['jira_url']}
JIRA_OAUTH1_CONSUMER_KEY={setup_state['consumer_key']}
JIRA_OAUTH1_PRIVATE_KEY_PATH=/path/to/your/private_key.pem
JIRA_OAUTH1_ACCESS_TOKEN={setup_state['access_token']}
JIRA_OAUTH1_ACCESS_TOKEN_SECRET={setup_state['access_token_secret']}

# Docker Configuration Example:
# {{
#   "mcpServers": {{
#     "mcp-atlassian": {{
#       "command": "docker",
#       "args": [
#         "run", "-i",
#         "-e", "JIRA_URL",
#         "-e", "JIRA_OAUTH1_CONSUMER_KEY",
#         "-e", "JIRA_OAUTH1_PRIVATE_KEY_PATH",
#         "-e", "JIRA_OAUTH1_ACCESS_TOKEN",
#         "-e", "JIRA_OAUTH1_ACCESS_TOKEN_SECRET",
#         "-v", "/path/to/your/private_key.pem:/app/private_key.pem:ro",
#         "ghcr.io/sooperset/mcp-atlassian:latest"
#       ],
#       "env": {{
#         "JIRA_URL": "{setup_state['jira_url']}",
#         "JIRA_OAUTH1_CONSUMER_KEY": "{setup_state['consumer_key']}",
#         "JIRA_OAUTH1_PRIVATE_KEY_PATH": "/app/private_key.pem",
#         "JIRA_OAUTH1_ACCESS_TOKEN": "{setup_state['access_token']}",
#         "JIRA_OAUTH1_ACCESS_TOKEN_SECRET": "{setup_state['access_token_secret']}"
#       }}
#     }}
#   }}
# }}
"""


@click.command()
@click.option(
    "--host",
    default="127.0.0.1",
    help="Host to bind the web server to"
)
@click.option(
    "--port",
    default=8080,
    help="Port to bind the web server to"
)
@click.option(
    "--verbose", "-v",
    is_flag=True,
    help="Enable verbose logging"
)
def oauth1_web_setup(host: str, port: int, verbose: bool) -> None:
    """Start the OAuth 1.0a web setup wizard."""
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    # Create HTML template if it doesn't exist
    create_html_template()
    
    click.echo("🌐 Starting OAuth 1.0a Web Setup Wizard")
    click.echo(f"📍 Open your browser and go to: http://{host}:{port}")
    click.echo("🔐 Follow the steps to complete OAuth1 authentication setup")
    click.echo("⏹️  Press Ctrl+C to stop the server")
    
    try:
        uvicorn.run(app, host=host, port=port, log_level="info" if verbose else "warning")
    except KeyboardInterrupt:
        click.echo("\n👋 OAuth1 Web Setup Wizard stopped")
    finally:
        # Cleanup
        if setup_state.get("private_key_path") and os.path.exists(setup_state["private_key_path"]):
            try:
                os.unlink(setup_state["private_key_path"])
            except Exception:
                pass


def create_html_template():
    """Create the HTML template for the web setup wizard."""
    template_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth 1.0a Setup Wizard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0052cc;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #0052cc;
            border-radius: 4px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input[type="text"], input[type="url"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 150px;
            font-family: monospace;
            resize: vertical;
        }
        button {
            background: #0052cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0043a3;
        }
        .auth-button {
            background: #36b37e;
            display: inline-block;
            text-decoration: none;
            color: white;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .auth-button:hover {
            background: #2a9d6b;
            text-decoration: none;
            color: white;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2e7d32;
        }
        .config-output {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
        }
        .restart-link {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        .restart-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 OAuth 1.0a Setup Wizard</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Configure OAuth 1.0a authentication for Jira Server/Data Center
        </p>

        {% if error %}
        <div class="error">
            <strong>Error:</strong> {{ error }}
        </div>
        {% endif %}

        {% if step == "config" %}
        <div class="step">
            <h3>Step 1: Configuration</h3>
            <p>Enter your Jira Server/Data Center details and OAuth configuration:</p>
        </div>

        <form method="post" action="/configure">
            <div class="form-group">
                <label for="jira_url">Jira Server/Data Center URL:</label>
                <input type="url" id="jira_url" name="jira_url" placeholder="https://jira.your-company.com" required>
                <small style="color: #666;">The base URL of your Jira Server/Data Center instance</small>
            </div>

            <div class="form-group">
                <label for="consumer_key">OAuth Consumer Key:</label>
                <input type="text" id="consumer_key" name="consumer_key" placeholder="your-oauth-consumer-key" required>
                <small style="color: #666;">The consumer key from your Jira Application Links configuration</small>
            </div>

            <div class="form-group">
                <label for="private_key">RSA Private Key:</label>
                <textarea id="private_key" name="private_key" placeholder="-----BEGIN RSA PRIVATE KEY-----
...
-----END RSA PRIVATE KEY-----" required></textarea>
                <small style="color: #666;">Paste your RSA private key in PEM format (including BEGIN/END lines)</small>
            </div>

            <button type="submit">Get Request Token</button>
        </form>

        {% elif step == "authorize" %}
        <div class="step">
            <h3>Step 2: Authorization</h3>
            <p>Request token obtained successfully! Now you need to authorize the application.</p>
        </div>

        <div style="margin: 20px 0;">
            <p><strong>Request Token:</strong> <code>{{ request_token }}</code></p>
            <p>Click the button below to open the authorization page in Jira:</p>
            <a href="{{ auth_url }}" target="_blank" class="auth-button">🔗 Authorize in Jira</a>
        </div>

        <div class="step">
            <h3>Step 3: Verification</h3>
            <p>After authorizing in Jira, you'll receive a verification code. Enter it below:</p>
        </div>

        <form method="post" action="/verify">
            <div class="form-group">
                <label for="verifier">Verification Code:</label>
                <input type="text" id="verifier" name="verifier" placeholder="Enter the verification code from Jira" required>
                <small style="color: #666;">The verification code shown after authorization</small>
            </div>

            <button type="submit">Get Access Token</button>
            <a href="/restart" class="restart-link">Start Over</a>
        </form>

        {% elif step == "complete" %}
        <div class="success">
            <h3>🎉 Setup Complete!</h3>
            <p>OAuth 1.0a authentication has been configured successfully.</p>
        </div>

        <div class="step">
            <h3>Your Configuration:</h3>
            <p>Copy the configuration below to your environment file or MCP settings:</p>
        </div>

        <div class="config-output">{{ config_content }}</div>

        <div style="margin-top: 20px;">
            <button onclick="copyConfig()">📋 Copy Configuration</button>
            <a href="/restart" class="restart-link">Setup Another Instance</a>
        </div>

        <div class="step" style="margin-top: 30px;">
            <h3>Next Steps:</h3>
            <ol>
                <li>Save your private key file to a secure location</li>
                <li>Update the <code>JIRA_OAUTH1_PRIVATE_KEY_PATH</code> to point to your private key file</li>
                <li>Add the environment variables to your MCP configuration</li>
                <li>Test the connection with your Jira instance</li>
            </ol>
        </div>
        {% endif %}
    </div>

    <script>
        function copyConfig() {
            const configText = document.querySelector('.config-output').textContent;
            navigator.clipboard.writeText(configText).then(() => {
                alert('Configuration copied to clipboard!');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = configText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Configuration copied to clipboard!');
            });
        }
    </script>
</body>
</html>'''
    
    template_path = templates_dir / "oauth1_setup.html"
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(template_content)


if __name__ == "__main__":
    oauth1_web_setup()
