#!/usr/bin/env python3
"""
使用已获取的访问令牌调用 JIRA API 的示例
假设你已经有了访问令牌和令牌密钥
"""

import requests
from requests_oauthlib import OAuth1Session
import json

def main():
    # 配置信息 - 请替换为您的实际信息
    JIRA_URL = "https://jira.qianxin-inc.cn"  # Replace with your JIRA URL
    CONSUMER_KEY = "mcp-atlassian-oauth-client"  # Replace with your consumer key
    PRIVATE_KEY_FILE = "../jira_privatekey.pem"  # Path to your private key file
    
    # 已获取的访问令牌信息（从 OAuth 流程中获得）
    ACCESS_TOKEN = "rklVkOmDAsmym7Af5bJIEDVAhba8dzUO"  # 替换为您获取到的访问令牌
    ACCESS_TOKEN_SECRET = "LGixld70OGHUwesZt8SlWi7Slz2bHi3J"  # 替换为您获取到的访问令牌密钥
    
    # 读取私钥
    with open(PRIVATE_KEY_FILE, 'r') as f:
        private_key = f.read()
    
    # 创建 OAuth1Session（使用访问令牌）
    oauth = OAuth1Session(
        client_key=CONSUMER_KEY,
        signature_method='RSA-SHA1',
        rsa_key=private_key,
        resource_owner_key=ACCESS_TOKEN,
        # resource_owner_secret=ACCESS_TOKEN_SECRET
    )
    
    print("使用访问令牌调用 JIRA API")
    print("=" * 50)
    
    # 示例 1: 获取当前用户信息
    print("1. 获取当前用户信息...")
    url = f"{JIRA_URL}/rest/api/2/myself"
    
    try:
        response = oauth.get(url)
        if response.status_code == 200:
            user_data = response.json()
            print("✓ 成功获取用户信息")
            print(f"  显示名称: {user_data.get('displayName', 'N/A')}")
            print(f"  邮箱: {user_data.get('emailAddress', 'N/A')}")
            print(f"  用户名: {user_data.get('name', 'N/A')}")
        else:
            print(f"✗ 请求失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 请求出错: {e}")
    
    # 示例 2: 搜索问题（前5个）
    print("\n2. 搜索问题...")
    url = f"{JIRA_URL}/rest/api/2/search"
    params = {"maxResults": 5}
    
    try:
        response = oauth.get(url, params=params)
        if response.status_code == 200:
            issues_data = response.json()
            print("✓ 成功搜索问题")
            print(f"  总共找到 {issues_data.get('total', 0)} 个问题")
            print("  前5个问题:")
            for issue in issues_data.get('issues', []):
                print(f"    - {issue['key']}: {issue['fields'].get('summary', 'N/A')}")
        else:
            print(f"✗ 请求失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 请求出错: {e}")
    
    # 示例 3: 获取项目列表
    print("\n3. 获取项目列表...")
    url = f"{JIRA_URL}/rest/api/2/project"
    
    try:
        response = oauth.get(url)
        if response.status_code == 200:
            projects_data = response.json()
            print("✓ 成功获取项目列表")
            print("  项目列表 (前5个):")
            for project in projects_data[:5]:  # 只显示前5个项目
                print(f"    - {project['key']}: {project.get('name', 'N/A')}")
        else:
            print(f"✗ 请求失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 请求出错: {e}")

if __name__ == "__main__":
    main()