"""Integration tests for OAuth1 authentication functionality."""

import os
import tempfile
from unittest.mock import Mock, patch

import pytest
import requests

from mcp_atlassian.jira.client import <PERSON>ra<PERSON><PERSON>
from mcp_atlassian.jira.config import JiraConfig


@pytest.fixture
def temp_private_key():
    """Create a temporary private key file for testing."""
    private_key_content = """-----B<PERSON>IN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abc<PERSON>f1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
-----END RSA PRIVATE KEY-----"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as f:
        f.write(private_key_content)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    os.unlink(temp_path)


@pytest.fixture
def oauth1_config(temp_private_key):
    """Create an OAuth1 JiraConfig for testing."""
    return JiraConfig(
        url="https://jira.example.com",
        auth_type="oauth1",
        oauth1_consumer_key="test_consumer_key",
        oauth1_private_key_path=temp_private_key,
        oauth1_access_token="test_access_token",
        oauth1_access_token_secret="test_access_token_secret"
    )


class TestOAuth1Integration:
    """Integration tests for OAuth1 authentication."""
    
    @patch('mcp_atlassian.jira.client.Jira')
    @patch('mcp_atlassian.utils.oauth1.configure_oauth1_session')
    def test_oauth1_client_initialization(self, mock_configure_oauth1, mock_jira, oauth1_config):
        """Test that JiraClient initializes correctly with OAuth1 configuration."""
        # Mock successful OAuth1 session configuration
        mock_configure_oauth1.return_value = True
        
        # Mock Jira client
        mock_jira_instance = Mock()
        mock_jira.return_value = mock_jira_instance
        
        # Initialize client
        client = JiraClient(config=oauth1_config)
        
        # Verify OAuth1 session was configured
        mock_configure_oauth1.assert_called_once()
        
        # Verify Jira client was initialized with session
        mock_jira.assert_called_once()
        call_args = mock_jira.call_args
        assert call_args[1]['url'] == 'https://jira.example.com'
        assert 'session' in call_args[1]
        assert call_args[1]['cloud'] is False  # Server/Data Center
        assert call_args[1]['verify_ssl'] is True
        
        # Verify client has the correct configuration
        assert client.config.auth_type == "oauth1"
        assert client.config.oauth1_consumer_key == "test_consumer_key"
        assert client.config.oauth1_access_token == "test_access_token"
    
    @patch('mcp_atlassian.utils.oauth1.configure_oauth1_session')
    def test_oauth1_session_configuration_failure(self, mock_configure_oauth1, oauth1_config):
        """Test handling of OAuth1 session configuration failure."""
        # Mock failed OAuth1 session configuration
        mock_configure_oauth1.return_value = False
        
        # Should raise authentication error
        with pytest.raises(Exception, match="Failed to configure OAuth1 session"):
            JiraClient(config=oauth1_config)
    
    def test_oauth1_config_validation(self, temp_private_key):
        """Test OAuth1 configuration validation."""
        # Complete configuration should be valid
        complete_config = JiraConfig(
            url="https://jira.example.com",
            auth_type="oauth1",
            oauth1_consumer_key="test_consumer",
            oauth1_private_key_path=temp_private_key,
            oauth1_access_token="test_token",
            oauth1_access_token_secret="test_secret"
        )
        assert complete_config.is_auth_configured() is True
        
        # Missing consumer key should be invalid
        missing_consumer = JiraConfig(
            url="https://jira.example.com",
            auth_type="oauth1",
            oauth1_private_key_path=temp_private_key,
            oauth1_access_token="test_token",
            oauth1_access_token_secret="test_secret"
        )
        assert missing_consumer.is_auth_configured() is False
        
        # Missing private key path should be invalid
        missing_key = JiraConfig(
            url="https://jira.example.com",
            auth_type="oauth1",
            oauth1_consumer_key="test_consumer",
            oauth1_access_token="test_token",
            oauth1_access_token_secret="test_secret"
        )
        assert missing_key.is_auth_configured() is False
        
        # Missing access token secret should be invalid
        missing_secret = JiraConfig(
            url="https://jira.example.com",
            auth_type="oauth1",
            oauth1_consumer_key="test_consumer",
            oauth1_private_key_path=temp_private_key,
            oauth1_access_token="test_token"
        )
        assert missing_secret.is_auth_configured() is False
    
    def test_oauth1_config_from_env(self, temp_private_key):
        """Test loading OAuth1 configuration from environment variables."""
        with patch.dict(os.environ, {
            'JIRA_URL': 'https://jira.example.com',
            'JIRA_OAUTH1_CONSUMER_KEY': 'env_consumer_key',
            'JIRA_OAUTH1_PRIVATE_KEY_PATH': temp_private_key,
            'JIRA_OAUTH1_ACCESS_TOKEN': 'env_access_token',
            'JIRA_OAUTH1_ACCESS_TOKEN_SECRET': 'env_access_secret'
        }, clear=True):
            config = JiraConfig.from_env()
            
            assert config.auth_type == "oauth1"
            assert config.oauth1_consumer_key == "env_consumer_key"
            assert config.oauth1_private_key_path == temp_private_key
            assert config.oauth1_access_token == "env_access_token"
            assert config.oauth1_access_token_secret == "env_access_secret"
            assert config.is_auth_configured() is True
    
    @patch('mcp_atlassian.jira.client.Jira')
    @patch('mcp_atlassian.utils.oauth1.configure_oauth1_session')
    def test_oauth1_api_call_simulation(self, mock_configure_oauth1, mock_jira, oauth1_config):
        """Test simulated API call with OAuth1 authentication."""
        # Mock successful OAuth1 session configuration
        mock_configure_oauth1.return_value = True
        
        # Mock Jira client and API response
        mock_jira_instance = Mock()
        mock_jira.return_value = mock_jira_instance
        
        # Mock a successful API call
        mock_jira_instance.myself.return_value = {
            'displayName': 'Test User',
            'emailAddress': '<EMAIL>',
            'name': 'testuser'
        }
        
        # Initialize client and make API call
        client = JiraClient(config=oauth1_config)
        user_info = client.jira.myself()
        
        # Verify the API call was made and returned expected data
        assert user_info['displayName'] == 'Test User'
        assert user_info['emailAddress'] == '<EMAIL>'
        assert user_info['name'] == 'testuser'
        
        # Verify OAuth1 session was configured
        mock_configure_oauth1.assert_called_once()


@pytest.mark.skipif(
    not all([
        os.getenv("JIRA_URL"),
        os.getenv("JIRA_OAUTH1_CONSUMER_KEY"),
        os.getenv("JIRA_OAUTH1_PRIVATE_KEY_PATH"),
        os.getenv("JIRA_OAUTH1_ACCESS_TOKEN"),
        os.getenv("JIRA_OAUTH1_ACCESS_TOKEN_SECRET")
    ]),
    reason="OAuth1 environment variables not set for real integration test"
)
class TestOAuth1RealIntegration:
    """Real integration tests with actual Jira instance (requires OAuth1 setup)."""
    
    def test_real_oauth1_authentication(self):
        """Test real OAuth1 authentication with actual Jira instance."""
        # This test requires actual OAuth1 credentials to be set in environment
        config = JiraConfig.from_env()
        assert config.auth_type == "oauth1"
        
        # Initialize client (this will test real OAuth1 authentication)
        client = JiraClient(config=config)
        
        # Test a simple API call
        try:
            user_info = client.jira.myself()
            assert 'displayName' in user_info
            assert 'emailAddress' in user_info
            print(f"✅ OAuth1 authentication successful for user: {user_info['displayName']}")
        except Exception as e:
            pytest.fail(f"OAuth1 authentication failed: {e}")
    
    def test_real_oauth1_api_calls(self):
        """Test various API calls with real OAuth1 authentication."""
        config = JiraConfig.from_env()
        client = JiraClient(config=config)
        
        try:
            # Test getting projects
            projects = client.jira.projects()
            assert isinstance(projects, list)
            print(f"✅ Retrieved {len(projects)} projects")
            
            # Test searching issues (limit to 1 for speed)
            issues = client.jira.search_issues("", maxResults=1)
            assert 'issues' in issues
            print(f"✅ Search issues successful, total: {issues.get('total', 0)}")
            
        except Exception as e:
            pytest.fail(f"OAuth1 API calls failed: {e}")
