#!/usr/bin/env python3
"""
MCP Client OAuth1 Authentication Example

This example demonstrates how to use OAuth1 authentication with an MCP client
connecting to the mcp-atlassian server via SSE or streamable-HTTP transport.
"""

import asyncio
import os
import sys
import urllib.parse
from pathlib import Path

# Add the src directory to the path for local development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.client.sse import sse_client
    from mcp.client.streamablehttp import streamablehttp_client
except ImportError:
    print("❌ MCP client library not found. Install with: pip install mcp")
    sys.exit(1)


async def test_oauth1_with_sse():
    """Test OAuth1 authentication with SSE transport."""
    print("🔐 Testing OAuth1 Authentication with SSE Transport")
    print("=" * 60)
    
    # OAuth1 credentials (replace with your actual credentials)
    oauth1_access_token = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN", "rklVkOmDAsmym7Af5bJIEDVAhba8dzUO")
    oauth1_access_token_secret = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN_SECRET", "LGixld70OGHUwesZt8SlWi7Slz2bHi3J")
    oauth1_consumer_key = os.getenv("JIRA_OAUTH1_CONSUMER_KEY", "your_consumer_key")
    oauth1_private_key = os.getenv("JIRA_OAUTH1_PRIVATE_KEY_CONTENT", "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----")
    jira_url = os.getenv("JIRA_URL", "https://jira.qianxin-inc.cn")
    
    # Build SSE URL with OAuth1 query parameters
    base_url = "http://localhost:9000/sse"
    query_params = {
        "oauth1_access_token": oauth1_access_token,
        "oauth1_access_token_secret": oauth1_access_token_secret,
        "oauth1_consumer_key": oauth1_consumer_key,
        "oauth1_private_key": oauth1_private_key,
        "oauth1_jira_url": jira_url
    }
    
    # URL encode the query parameters
    query_string = urllib.parse.urlencode(query_params)
    sse_url = f"{base_url}?{query_string}"
    
    print(f"📡 Connecting to SSE endpoint: {base_url}")
    print(f"🔑 OAuth1 Access Token: {oauth1_access_token[:10]}...")
    print(f"🔑 OAuth1 Consumer Key: {oauth1_consumer_key}")
    print(f"🌐 Jira URL: {jira_url}")
    
    try:
        async with sse_client(sse_url) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                # Initialize the connection
                await session.initialize()
                print("✅ MCP session initialized successfully")
                
                # Test: Get current user
                print("\n🧪 Test 1: Get Current User")
                try:
                    result = await session.call_tool("jira_get_current_user", {})
                    print(f"✅ Success: {result}")
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                # Test: List projects
                print("\n🧪 Test 2: List Projects")
                try:
                    result = await session.call_tool("jira_list_projects", {})
                    print(f"✅ Success: Found projects")
                    if result and hasattr(result, 'content'):
                        print(f"📊 Projects: {len(result.content) if result.content else 0}")
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                return True
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


async def test_oauth1_with_streamable_http():
    """Test OAuth1 authentication with streamable-HTTP transport."""
    print("\n🔐 Testing OAuth1 Authentication with Streamable-HTTP Transport")
    print("=" * 60)
    
    # OAuth1 credentials
    oauth1_access_token = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN", "rklVkOmDAsmym7Af5bJIEDVAhba8dzUO")
    oauth1_access_token_secret = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN_SECRET", "LGixld70OGHUwesZt8SlWi7Slz2bHi3J")
    oauth1_consumer_key = os.getenv("JIRA_OAUTH1_CONSUMER_KEY", "your_consumer_key")
    oauth1_private_key = os.getenv("JIRA_OAUTH1_PRIVATE_KEY_CONTENT", "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----")
    jira_url = os.getenv("JIRA_URL", "https://jira.qianxin-inc.cn")
    
    # Prepare headers for OAuth1 authentication
    headers = {
        "Authorization": f"OAuth1 {oauth1_access_token}:{oauth1_access_token_secret}",
        "X-OAuth1-Consumer-Key": oauth1_consumer_key,
        "X-OAuth1-Private-Key": oauth1_private_key,
        "X-Jira-URL": jira_url
    }
    
    print(f"📡 Connecting to streamable-HTTP endpoint: http://localhost:9000/mcp")
    print(f"🔑 OAuth1 Access Token: {oauth1_access_token[:10]}...")
    print(f"🔑 OAuth1 Consumer Key: {oauth1_consumer_key}")
    print(f"🌐 Jira URL: {jira_url}")
    
    try:
        async with streamablehttp_client("http://localhost:9000/mcp", headers=headers) as (read_stream, write_stream, _):
            async with ClientSession(read_stream, write_stream) as session:
                # Initialize the connection
                await session.initialize()
                print("✅ MCP session initialized successfully")
                
                # Test: Get current user
                print("\n🧪 Test 1: Get Current User")
                try:
                    result = await session.call_tool("jira_get_current_user", {})
                    print(f"✅ Success: {result}")
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                # Test: List projects
                print("\n🧪 Test 2: List Projects")
                try:
                    result = await session.call_tool("jira_list_projects", {})
                    print(f"✅ Success: Found projects")
                    if result and hasattr(result, 'content'):
                        print(f"📊 Projects: {len(result.content) if result.content else 0}")
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                return True
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


def show_mcp_client_config_examples():
    """Show MCP client configuration examples for different IDEs."""
    print("\n📚 MCP Client Configuration Examples")
    print("=" * 60)
    
    print("1. Claude Desktop / Cursor (SSE with Query Parameters):")
    print("""
{
  "mcpServers": {
    "mcp-atlassian": {
      "url": "http://localhost:9000/sse?oauth1_access_token=rklVkOmDAsmym7Af5bJIEDVAhba8dzUO&oauth1_access_token_secret=LGixld70OGHUwesZt8SlWi7Slz2bHi3J&oauth1_consumer_key=your_consumer_key&oauth1_private_key=-----BEGIN%20RSA%20PRIVATE%20KEY-----%0A...%0A-----END%20RSA%20PRIVATE%20KEY-----&oauth1_jira_url=https://jira.qianxin-inc.cn"
    }
  }
}
""")
    
    print("2. MCP Client with Headers (Streamable-HTTP):")
    print("""
{
  "mcpServers": {
    "mcp-atlassian": {
      "url": "http://localhost:9000/mcp",
      "headers": {
        "Authorization": "OAuth1 rklVkOmDAsmym7Af5bJIEDVAhba8dzUO:LGixld70OGHUwesZt8SlWi7Slz2bHi3J",
        "X-OAuth1-Consumer-Key": "your_consumer_key",
        "X-OAuth1-Private-Key": "-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----",
        "X-Jira-URL": "https://jira.qianxin-inc.cn"
      }
    }
  }
}
""")
    
    print("3. Environment Variables (for easier management):")
    print("""
# Set these environment variables:
export JIRA_OAUTH1_ACCESS_TOKEN="rklVkOmDAsmym7Af5bJIEDVAhba8dzUO"
export JIRA_OAUTH1_ACCESS_TOKEN_SECRET="LGixld70OGHUwesZt8SlWi7Slz2bHi3J"
export JIRA_OAUTH1_CONSUMER_KEY="your_consumer_key"
export JIRA_OAUTH1_PRIVATE_KEY_CONTENT="-----BEGIN RSA PRIVATE KEY-----
...
-----END RSA PRIVATE KEY-----"
export JIRA_URL="https://jira.qianxin-inc.cn"

# Then use in MCP client config:
{
  "mcpServers": {
    "mcp-atlassian": {
      "url": "http://localhost:9000/sse?oauth1_access_token=${JIRA_OAUTH1_ACCESS_TOKEN}&oauth1_access_token_secret=${JIRA_OAUTH1_ACCESS_TOKEN_SECRET}&oauth1_consumer_key=${JIRA_OAUTH1_CONSUMER_KEY}&oauth1_private_key=${JIRA_OAUTH1_PRIVATE_KEY_CONTENT}&oauth1_jira_url=${JIRA_URL}"
    }
  }
}
""")


async def main():
    """Main function to test OAuth1 authentication with MCP client."""
    print("🚀 MCP Client OAuth1 Authentication Test")
    print("=" * 60)
    print("This test demonstrates OAuth1 authentication with MCP client")
    print("Make sure the MCP server is running on localhost:9000")
    print()
    
    # Check if required credentials are available
    required_vars = [
        "JIRA_OAUTH1_ACCESS_TOKEN",
        "JIRA_OAUTH1_ACCESS_TOKEN_SECRET", 
        "JIRA_OAUTH1_CONSUMER_KEY",
        "JIRA_OAUTH1_PRIVATE_KEY_CONTENT",
        "JIRA_URL"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print("⚠️  Some OAuth1 credentials are missing from environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Using default/example values for demonstration")
        print("   Set the environment variables for real testing")
        print()
    
    # Test SSE transport
    sse_success = await test_oauth1_with_sse()
    
    # Test streamable-HTTP transport
    http_success = await test_oauth1_with_streamable_http()
    
    # Show configuration examples
    show_mcp_client_config_examples()
    
    if sse_success or http_success:
        print("\n✅ OAuth1 MCP client authentication test completed!")
        print("At least one transport method worked successfully")
    else:
        print("\n❌ OAuth1 MCP client authentication test failed")
        print("Make sure the MCP server is running and OAuth1 credentials are correct")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
