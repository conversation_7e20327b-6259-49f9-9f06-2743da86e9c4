#!/usr/bin/env python3
"""
OAuth 1.0a Dynamic Authentication Test

This script demonstrates how to use OAuth 1.0a authentication with mcp-atlassian
by passing OAuth1 credentials via HTTP headers, similar to how MCP clients would
interact with the service.

This simulates the actual usage pattern where OAuth1 credentials are provided
dynamically per request rather than being configured at startup.
"""

import base64
import json
import os
import sys
from pathlib import Path

import requests

# Add the src directory to the path for local development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_oauth1_dynamic_auth():
    """Test OAuth1 dynamic authentication via HTTP headers."""
    print("🔐 Testing OAuth 1.0a Dynamic Authentication")
    print("=" * 60)
    
    # Check if OAuth1 credentials are available
    required_vars = [
        "JIRA_URL",
        "JIRA_OAUTH1_CONSUMER_KEY",
        "JIRA_OAUTH1_PRIVATE_KEY_CONTENT",  # Note: content, not path
        "JIRA_OAUTH1_ACCESS_TOKEN",
        "JIRA_OAUTH1_ACCESS_TOKEN_SECRET"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 For dynamic OAuth1 testing, set these environment variables:")
        print("   export JIRA_URL='https://your-jira-server.com'")
        print("   export JIRA_OAUTH1_CONSUMER_KEY='your-consumer-key'")
        print("   export JIRA_OAUTH1_PRIVATE_KEY_CONTENT='-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----'")
        print("   export JIRA_OAUTH1_ACCESS_TOKEN='your-access-token'")
        print("   export JIRA_OAUTH1_ACCESS_TOKEN_SECRET='your-access-token-secret'")
        return False
    
    # Get credentials from environment
    jira_url = os.getenv("JIRA_URL")
    consumer_key = os.getenv("JIRA_OAUTH1_CONSUMER_KEY")
    private_key_content = os.getenv("JIRA_OAUTH1_PRIVATE_KEY_CONTENT")
    access_token = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN")
    access_token_secret = os.getenv("JIRA_OAUTH1_ACCESS_TOKEN_SECRET")
    
    print(f"📋 OAuth1 Configuration:")
    print(f"   Jira URL: {jira_url}")
    print(f"   Consumer Key: {consumer_key}")
    print(f"   Access Token: {access_token[:10]}...")
    print(f"   Access Token Secret: {access_token_secret[:10]}...")
    print(f"   Private Key: {'✅ Present' if private_key_content else '❌ Missing'}")
    
    # Prepare headers for OAuth1 authentication
    # Format: Authorization: OAuth1 access_token:access_token_secret
    auth_header = f"OAuth1 {access_token}:{access_token_secret}"
    
    headers = {
        "Authorization": auth_header,
        "X-OAuth1-Consumer-Key": consumer_key,
        "X-OAuth1-Private-Key": private_key_content,
        "X-Jira-URL": jira_url,
        "Content-Type": "application/json"
    }
    
    print(f"\n🌐 Testing with MCP Server...")
    print(f"   Authorization Header: OAuth1 {access_token[:10]}...:{access_token_secret[:10]}...")
    print(f"   Consumer Key Header: {consumer_key}")
    print(f"   Jira URL Header: {jira_url}")
    
    # Test MCP server endpoint (assuming it's running on localhost:8000)
    mcp_server_url = "http://localhost:8000"
    
    # Test 1: Get current user info
    print(f"\n🧪 Test 1: Get Current User Info")
    try:
        # Create MCP request for getting current user
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": "jira_get_current_user",
                "arguments": {}
            }
        }
        
        response = requests.post(
            f"{mcp_server_url}/mcp/v1/tools/call",
            headers=headers,
            json=mcp_request,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success: {response.status_code}")
            print(f"   📄 Response: {json.dumps(result, indent=2)}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"   ⚠️  MCP Server not running on {mcp_server_url}")
        print(f"   💡 Start the MCP server first:")
        print(f"      python -m mcp_atlassian --transport http")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: List projects
    print(f"\n🧪 Test 2: List Projects")
    try:
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "jira_list_projects",
                "arguments": {}
            }
        }
        
        response = requests.post(
            f"{mcp_server_url}/mcp/v1/tools/call",
            headers=headers,
            json=mcp_request,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success: {response.status_code}")
            if "result" in result and "content" in result["result"]:
                content = result["result"]["content"]
                if isinstance(content, list) and content:
                    # Parse the content to show project info
                    try:
                        project_data = json.loads(content[0]["text"]) if content[0]["type"] == "text" else content[0]
                        if isinstance(project_data, list):
                            print(f"   📊 Found {len(project_data)} projects")
                            for i, project in enumerate(project_data[:3]):  # Show first 3
                                print(f"      {i+1}. {project.get('key', 'N/A')}: {project.get('name', 'N/A')}")
                        else:
                            print(f"   📄 Response: {json.dumps(result, indent=2)}")
                    except:
                        print(f"   📄 Response: {json.dumps(result, indent=2)}")
                else:
                    print(f"   📄 Response: {json.dumps(result, indent=2)}")
            else:
                print(f"   📄 Response: {json.dumps(result, indent=2)}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    print(f"\n🎉 OAuth 1.0a Dynamic Authentication Test Completed!")
    print(f"\n📝 Summary:")
    print(f"   - OAuth1 credentials were passed via HTTP headers")
    print(f"   - MCP server processed the dynamic authentication")
    print(f"   - API calls were made using the provided OAuth1 tokens")
    print(f"   - This demonstrates the dynamic authentication pattern")
    
    return True


def show_usage_examples():
    """Show usage examples for OAuth1 dynamic authentication."""
    print(f"\n📚 OAuth1 Dynamic Authentication Usage Examples")
    print(f"=" * 60)
    
    print(f"1. HTTP Headers Format:")
    print(f"""
Authorization: OAuth1 <access_token>:<access_token_secret>
X-OAuth1-Consumer-Key: <consumer_key>
X-OAuth1-Private-Key: <private_key_content>
X-Jira-URL: <jira_server_url>
""")
    
    print(f"2. cURL Example:")
    print(f"""
curl -X POST http://localhost:8000/mcp/v1/tools/call \\
  -H "Authorization: OAuth1 your_access_token:your_access_secret" \\
  -H "X-OAuth1-Consumer-Key: your_consumer_key" \\
  -H "X-OAuth1-Private-Key: -----BEGIN RSA PRIVATE KEY-----..." \\
  -H "X-Jira-URL: https://your-jira-server.com" \\
  -H "Content-Type: application/json" \\
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "jira_get_current_user",
      "arguments": {}
    }
  }'
""")
    
    print(f"3. Python Requests Example:")
    print(f"""
import requests

headers = {
    "Authorization": "OAuth1 your_access_token:your_access_secret",
    "X-OAuth1-Consumer-Key": "your_consumer_key",
    "X-OAuth1-Private-Key": "-----BEGIN RSA PRIVATE KEY-----...",
    "X-Jira-URL": "https://your-jira-server.com",
    "Content-Type": "application/json"
}

response = requests.post(
    "http://localhost:8000/mcp/v1/tools/call",
    headers=headers,
    json={
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "jira_get_current_user",
            "arguments": {}
        }
    }
)
""")


def main():
    """Main function to run the OAuth1 dynamic authentication test."""
    print("🚀 MCP Atlassian OAuth 1.0a Dynamic Authentication Test")
    print("=" * 60)
    print("This test demonstrates OAuth1 authentication via HTTP headers")
    print("(the way MCP clients would actually use the service)")
    print()
    
    # Run the test
    success = test_oauth1_dynamic_auth()
    
    # Show usage examples
    show_usage_examples()
    
    if success:
        print("\n✅ OAuth1 dynamic authentication test completed successfully!")
        print("The MCP server correctly handled OAuth1 credentials via HTTP headers")
    else:
        print("\n❌ OAuth1 dynamic authentication test failed")
        print("Check your configuration and ensure the MCP server is running")
        sys.exit(1)


if __name__ == "__main__":
    main()
