#!/usr/bin/env python3
"""
OAuth 1.0a Example for MCP Atlassian

This example demonstrates how to configure and use OAuth 1.0a authentication
with Atlassian Server/Data Center instances.

Prerequisites:
1. You have an Atlassian Server/Data Center instance
2. You have created an OAuth consumer application in your Atlassian instance
3. You have generated RSA key pair and configured the public key
4. You have obtained access token and secret through authorization

Environment Variables Required:
- ATLASSIAN_OAUTH1_CONSUMER_KEY: Your consumer key
- ATLASSIAN_OAUTH1_KEY_CERT: Your RSA private key (PEM format)
- ATLASSIAN_OAUTH1_ACCESS_TOKEN: Your access token
- ATLASSIAN_OAUTH1_ACCESS_TOKEN_SECRET: Your access token secret
- JIRA_URL: Your Jira Server/Data Center URL
- CONFLUENCE_URL: Your Confluence Server/Data Center URL (optional)
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the package
sys.path.append(str(Path(__file__).parent.parent))

from src.mcp_atlassian.utils.oauth import OAuth1aConfig, get_oauth_config_from_env
from src.mcp_atlassian.jira.config import JiraConfig
from src.mcp_atlassian.jira.client import JiraClient
from src.mcp_atlassian.confluence.config import ConfluenceConfig
from src.mcp_atlassian.confluence.client import ConfluenceClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_oauth1a_configuration():
    """Test OAuth 1.0a configuration from environment variables."""
    logger.info("Testing OAuth 1.0a configuration...")
    
    # Get OAuth configuration from environment
    oauth_config = get_oauth_config_from_env()
    
    if not oauth_config:
        logger.error("No OAuth configuration found in environment variables")
        return False
    
    if not isinstance(oauth_config, OAuth1aConfig):
        logger.error(f"Expected OAuth1aConfig, got {type(oauth_config)}")
        return False
    
    logger.info("✅ OAuth 1.0a configuration loaded successfully")
    logger.info(f"Consumer Key: {oauth_config.consumer_key}")
    logger.info(f"Signature Method: {oauth_config.signature_method}")
    logger.info(f"Access Token: {oauth_config.access_token[:10]}...")
    
    return True


def test_jira_oauth1a():
    """Test Jira client with OAuth 1.0a authentication."""
    logger.info("Testing Jira client with OAuth 1.0a...")
    
    jira_url = os.getenv("JIRA_URL")
    if not jira_url:
        logger.error("JIRA_URL environment variable is required")
        return False
    
    try:
        # Create Jira configuration
        jira_config = JiraConfig.from_env()
        if not jira_config:
            logger.error("Failed to create Jira configuration")
            return False
        
        if jira_config.auth_type != "oauth":
            logger.error(f"Expected OAuth auth type, got {jira_config.auth_type}")
            return False
        
        # Create Jira client
        jira_client = JiraClient(jira_config)
        
        # Test basic connectivity
        logger.info("Testing Jira connectivity...")
        projects = jira_client.get_projects()
        
        logger.info(f"✅ Successfully connected to Jira. Found {len(projects)} projects")
        if projects:
            logger.info(f"First project: {projects[0].get('name', 'Unknown')} ({projects[0].get('key', 'Unknown')})")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to test Jira OAuth 1.0a: {e}")
        return False


def test_confluence_oauth1a():
    """Test Confluence client with OAuth 1.0a authentication."""
    logger.info("Testing Confluence client with OAuth 1.0a...")
    
    confluence_url = os.getenv("CONFLUENCE_URL")
    if not confluence_url:
        logger.warning("CONFLUENCE_URL environment variable not set, skipping Confluence test")
        return True
    
    try:
        # Create Confluence configuration
        confluence_config = ConfluenceConfig.from_env()
        if not confluence_config:
            logger.error("Failed to create Confluence configuration")
            return False
        
        if confluence_config.auth_type != "oauth":
            logger.error(f"Expected OAuth auth type, got {confluence_config.auth_type}")
            return False
        
        # Create Confluence client
        confluence_client = ConfluenceClient(confluence_config)
        
        # Test basic connectivity
        logger.info("Testing Confluence connectivity...")
        spaces = confluence_client.get_spaces()
        
        logger.info(f"✅ Successfully connected to Confluence. Found {len(spaces)} spaces")
        if spaces:
            logger.info(f"First space: {spaces[0].get('name', 'Unknown')} ({spaces[0].get('key', 'Unknown')})")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to test Confluence OAuth 1.0a: {e}")
        return False


def main():
    """Main function to run all OAuth 1.0a tests."""
    logger.info("🚀 Starting OAuth 1.0a Example Tests")
    
    # Check required environment variables
    required_vars = [
        "ATLASSIAN_OAUTH1_CONSUMER_KEY",
        "ATLASSIAN_OAUTH1_KEY_CERT", 
        "ATLASSIAN_OAUTH1_ACCESS_TOKEN",
        "ATLASSIAN_OAUTH1_ACCESS_TOKEN_SECRET",
        "JIRA_URL"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables and try again.")
        return 1
    
    # Run tests
    tests = [
        ("OAuth 1.0a Configuration", test_oauth1a_configuration),
        ("Jira OAuth 1.0a", test_jira_oauth1a),
        ("Confluence OAuth 1.0a", test_confluence_oauth1a),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! OAuth 1.0a is working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
