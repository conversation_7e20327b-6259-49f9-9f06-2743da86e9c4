[project]
name = "mcp-atlassian"
dynamic = ["version"]
description = "The Model Context Protocol (MCP) Atlassian integration is an open-source implementation that bridges Atlassian products (Jira and Confluence) with AI language models following Anthropic's MCP specification. This project enables secure, contextual AI interactions with Atlassian tools while maintaining data privacy and security. Key features include:"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "atlassian-python-api>=4.0.0",
    "requests[socks]>=2.31.0",
    "requests-oauthlib>=1.3.1",
    "beautifulsoup4>=4.12.3",
    "httpx>=0.28.0",
    "mcp>=1.8.0,<2.0.0",
    "fastmcp>=2.3.4,<2.4.0",
    "python-dotenv>=1.0.1",
    "markdownify>=0.11.6",
    "markdown>=3.7.0",
    "markdown-to-confluence>=0.3.0,<0.4.0",
    "pydantic>=2.10.6",
    "trio>=0.29.0",
    "click>=8.1.7",
    "uvicorn>=0.27.1",
    "starlette>=0.37.1",
    "thefuzz>=0.22.1",
    "python-dateutil>=2.9.0.post0",
    "types-python-dateutil>=2.9.0.20241206",
    "keyring>=25.6.0",
    "cachetools>=5.0.0",
    "types-cachetools>=5.5.0.20240820",
]
[[project.authors]]
name = "sooperset"
email = "<EMAIL>"

[build-system]
requires = ["hatchling", "uv-dynamic-versioning>=0.7.0"]
build-backend = "hatchling.build"

[project.scripts]
mcp-atlassian = "mcp_atlassian:main"

[dependency-groups]
dev = [
    "uv>=0.1.0",
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.23.0",
    "pre-commit>=3.6.0",
    "ruff>=0.3.0",
    "black>=24.2.0",
    "mypy>=1.8.0",
    "mcp[cli]>=1.3.0"
]

[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]
line-length = 88
indent-width = 4
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "B", "W", "I", "N", "UP", "ANN", "S", "BLE", "FBT", "C4", "DTZ", "T10", "EM", "ISC", "ICN"]
ignore = ["ANN401", "EM101"]
fixable = ["ALL"]
unfixable = []
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S", "ANN", "B017"]
"tests/fixtures/*.py" = ["E501"]
"src/mcp_atlassian/server.py" = ["E501"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = false
warn_no_return = true
warn_unreachable = true
strict_equality = true
strict_optional = true
disallow_subclassing_any = true
warn_incomplete_stub = true
exclude = "^src/"
explicit_package_bases = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
check_untyped_defs = false

[[tool.mypy.overrides]]
module = "atlassian.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "markdownify.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "src.mcp_atlassian.*"
disallow_untyped_defs = false

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.uv-dynamic-versioning]
vcs = "git"
style = "pep440"
bump = true
fallback-version = "0.0.0"
